# 🚀 产品批量上传快速指南

## 最简单的方法（推荐）

### 第一步：准备图片
1. **选择免费图床服务**：
   - [ImgBB](https://imgbb.com/) - 免费，无需注册
   - [Imgur](https://imgur.com/) - 知名度高，需要注册
   - [Cloudinary](https://cloudinary.com/) - 专业服务，需要注册

2. **上传图片**：
   - 批量上传您的产品图片
   - 获取每张图片的直链URL

### 第二步：管理图片URL
1. 访问：`http://localhost:3000/admin/products/image-helper`
2. 逐个添加图片URL
3. 点击"复制JSON数组"

### 第三步：准备产品数据
1. 使用以下JSON模板：

```json
[
  {
    "name": "产品名称",
    "slug": "product-slug",
    "sku": "SKU001",
    "short_desc": "产品简短描述",
    "description": "详细描述",
    "regular_price": 100.00,
    "sale_price": null,
    "stock_status": "instock",
    "status": "draft",
    "category": "accessories",
    "images": [
      "粘贴从图片助手工具复制的URL数组"
    ],
    "features": ["特性1", "特性2"],
    "applications": ["应用1", "应用2"],
    "materials": ["材料1", "材料2"]
  }
]
```

### 第四步：批量上传
1. 访问：`http://localhost:3000/admin/products/bulk-upload`
2. 上传您的JSON文件
3. 产品将保存为草稿状态

### 第五步：发布产品
1. 访问：`http://localhost:3000/admin/products`
2. 查看草稿产品
3. 逐个检查并点击"Publish"发布

## 🔧 常见问题

### Q: 图片助手工具打不开？
**A**: 确保您已经登录管理后台，如果还是不行，请检查：
1. 浏览器控制台是否有错误
2. 网络连接是否正常
3. 尝试刷新页面

### Q: 批量上传失败？
**A**: 检查以下几点：
1. JSON格式是否正确
2. 图片URL是否可以直接访问
3. 产品名称是否重复
4. 是否已经登录管理后台

### Q: 产品在前台不显示？
**A**: 这是正常的，因为：
1. 产品默认保存为草稿状态
2. 只有发布状态的产品才会在前台显示
3. 在管理后台将产品状态改为"Publish"即可

### Q: 图片显示不出来？
**A**: 检查：
1. 图片URL是否正确
2. 图片服务器是否支持外链
3. 图片是否被删除

## 📝 重要提示

1. **产品状态**：默认为草稿，需要手动发布
2. **图片格式**：支持 JPG, PNG, GIF, WebP
3. **图片大小**：建议每张不超过 2MB
4. **URL有效性**：确保图片URL可以直接访问
5. **备份数据**：建议保存JSON文件作为备份

## 🎯 成功标志

- ✅ 批量上传显示"成功"
- ✅ 管理后台可以看到草稿产品
- ✅ 产品图片正常显示
- ✅ 发布后前台可以看到产品

## 📞 需要帮助？

如果遇到问题，请：
1. 检查浏览器控制台错误信息
2. 确认网络连接正常
3. 验证JSON格式正确性
4. 检查图片URL有效性
