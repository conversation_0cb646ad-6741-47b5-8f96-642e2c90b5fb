Set up the frontend according to the following prompt:
  <frontend-prompt>
  Create detailed components with these requirements:
  1. Use 'use client' directive for client-side components
  2. Make sure to concatenate strings correctly using backslash
  3. Style with Tailwind CSS utility classes for responsive design
  4. Use Lucide React for icons (from lucide-react package). Do NOT use other UI libraries unless requested
  5. Use stock photos from picsum.photos where appropriate, only valid URLs you know exist
  6. Configure next.config.js image remotePatterns to enable stock photos from picsum.photos
  7. Create root layout.tsx page that wraps necessary navigation items to all pages
  8. MUST implement the navigation elements items in their rightful place i.e. Left sidebar, Top header
  9. Accurately implement necessary grid layouts
  10. Follow proper import practices:
     - Use @/ path aliases
     - Keep component imports organized
     - Update current src/app/page.tsx with new comprehensive code
     - Don't forget root route (page.tsx) handling
     - You MUST complete the entire prompt before stopping
  </frontend-prompt>

  <summary_title>
Injection Molding Educational Platform UI Design
</summary_title>

<image_analysis>
1. Navigation Elements:
- Primary navigation: HOME, ABOUT, BLOG, QUIZ, PODCAST, CONTACT
- Logo "MoldAll" centered in top bar, white text on dark background
- Social media icons in top right (Facebook, Instagram, YouTube)
- Search icon in far right
- Navigation height: 60px
- Secondary CTA buttons: "START LEARNING" and "EXPLORE RESOURCES"

2. Layout Components:
- Full-width hero section (~1440px)
- Content container width: 1200px
- Two-column layout in main sections
- Padding: 40px (desktop)
- Responsive container with 15px side margins on mobile

3. Content Sections:
- Hero section with headline and description
- Quick links section with icons and text
- Video tutorial section
- About Us section
- Product showcase with prototype images
- Contact information block

4. Interactive Controls:
- Primary CTA button (green): "START LEARNING"
- Secondary button: "EXPLORE RESOURCES" with arrow
- Social media icons with hover states
- Search functionality in header
- WhatsApp floating button in bottom right

5. Colors:
- Primary Green: #00B140
- Secondary Green: #008631
- Background: #FFFFFF
- Text Dark: #333333
- Navigation Background: #1A1A1A

6. Grid/Layout Structure:
- 12-column grid system
- Main container max-width: 1200px
- Gutter width: 30px
- Responsive breakpoints at 768px, 992px, 1200px
</image_analysis>

<development_planning>
1. Project Structure:
```
src/
├── components/
│   ├── layout/
│   │   ├── Header
│   │   ├── Footer
│   │   └── Navigation
│   ├── features/
│   │   ├── Hero
│   │   ├── QuickLinks
│   │   └── VideoTutorial
│   └── shared/
├── assets/
├── styles/
├── hooks/
└── utils/
```

2. Key Features:
- Responsive navigation system
- Dynamic content loading
- Video integration
- Contact form functionality
- Social media integration
- Search functionality

3. State Management:
```typescript
interface AppState {
  navigation: {
    isMenuOpen: boolean,
    currentPage: string
  },
  content: {
    tutorials: Tutorial[],
    resources: Resource[]
  },
  user: {
    isAuthenticated: boolean,
    preferences: UserPreferences
  }
}
```

4. Component Architecture:
- Header (Navigation, Search, Social)
- Hero (Title, Description, CTAs)
- QuickLinks (IconCard, LinkButton)
- VideoSection (VideoPlayer, TutorialList)
- ContactForm (Form, ValidationSystem)

5. Responsive Breakpoints:
```scss
$breakpoints: (
  'mobile': 320px,
  'tablet': 768px,
  'desktop': 992px,
  'wide': 1200px
);
```
</development_planning>