-- 博客
create index if not exists idx_posts_slug       on public.posts    (slug);
-- 商品
create index if not exists idx_products_slug    on public.products (slug);
create index if not exists idx_products_sku     on public.products (sku);
-- 分类/标签
create index if not exists idx_prod_cat_slug    on public.product_categories (slug);
create index if not exists idx_prod_tag_slug    on public.product_tags       (slug);
-- 关联表加反向查询
create index if not exists idx_post_cat_catid   on public.post_categories    (category_id);
create index if not exists idx_prod_cat_catid   on public.product_to_category(category_id);
create index if not exists idx_prod_tag_tagid   on public.product_to_tag     (tag_id);


create table public.product_meta (
  id         bigint generated always as identity primary key,
  product_id bigint not null references public.products on delete cascade,
  key        text,
  value      text
);

alter table public.product_meta enable row level security;
create policy "作者可管自己的 product_meta"
  on public.product_meta for all
  using (
    auth.uid() = (select author_id from public.products where id = product_id)
  );

create table public.product_meta (
  id         bigint generated always as identity primary key,
  product_id bigint not null references public.products on delete cascade,
  key        text,
  value      text
);

alter table public.product_meta enable row level security;
create policy "作者可管自己的 product_meta"
  on public.product_meta for all
  using (
    auth.uid() = (select author_id from public.products where id = product_id)
  );

create index on public.posts (slug);
create index on public.products (slug);
create index on public.products (sku);
create index on public.post_categories (category_id);
create index on public.product_to_category (category_id);

create table public.product_variations (
  id            bigint generated always as identity primary key,
  parent_id     bigint references public.products on delete cascade,
  sku           text,
  attributes    jsonb,                        -- {"Color":"Red","Size":"L"}
  regular_price numeric(10,2),
  sale_price    numeric(10,2),
  stock_status  text default 'instock',
  stock_quantity integer,
  images        jsonb default '[]',
  meta          jsonb default '{}'
);

create table public.product_categories (
  id          bigint generated always as identity primary key,
  name        text not null,
  slug        text unique,
  description text
);

create table public.product_tags (
  id          bigint generated always as identity primary key,
  name        text not null,
  slug        text unique
);

create table public.product_to_category (
  product_id   bigint references public.products on delete cascade,
  category_id  bigint references public.product_categories on delete cascade,
  primary key (product_id, category_id)
);

create table public.product_to_tag (
  product_id bigint references public.products on delete cascade,
  tag_id     bigint references public.product_tags on delete cascade,
  primary key (product_id, tag_id)
);
create table public.products (
  id              bigint generated always as identity primary key,
  author_id       uuid references public.profiles(id) on delete set null,
  name            text not null,
  slug            text unique,
  sku             text,
  type            text default 'simple',            -- simple | variable | virtual
  short_desc      text,
  description     text,
  regular_price   numeric(10,2),
  sale_price      numeric(10,2),
  sale_start      date,
  sale_end        date,
  tax_status      text default 'taxable',
  stock_status    text default 'instock',           -- instock | outofstock
  stock_quantity  integer,
  weight          numeric(10,2),
  dim_length      numeric(10,2),
  dim_width       numeric(10,2),
  dim_height      numeric(10,2),
  attributes      jsonb default '[]',               -- [{"name":"Color","options":["Red"]}]
  images          jsonb default '[]',               -- [{id,url,alt}]
  status          text default 'draft',             -- draft | publish
  created_at      timestamptz default now(),
  updated_at      timestamptz default now(),
  meta            jsonb default '{}'
);

alter table public.products enable row level security;
create policy "作者管理自己的商品"
  on public.products for all
  using ( auth.uid() = author_id );

create policy "已发布商品对所有人可读"
  on public.products for select
  using ( status = 'publish' );

create table public.post_meta (
  id      bigint generated always as identity primary key,
  post_id bigint not null references public.posts on delete cascade,
  key     text,
  value   text
);


create table public.categories (
  id          bigint generated always as identity primary key,
  name        text not null,
  slug        text unique,
  description text
);

create table public.tags (
  id          bigint generated always as identity primary key,
  name        text not null,
  slug        text unique
);

create table public.post_categories (
  post_id     bigint references public.posts on delete cascade,
  category_id bigint references public.categories on delete cascade,
  primary key (post_id, category_id)
);

create table public.post_tags (
  post_id bigint references public.posts on delete cascade,
  tag_id  bigint references public.tags  on delete cascade,
  primary key (post_id, tag_id)
);


-- 文章 posts
create table public.posts (
  id            bigint generated always as identity primary key,
  author_id     uuid not null references public.profiles(id) on delete set null,
  title         text not null,
  slug          text unique,                    -- URL 别名
  content       text,                           -- 正文（HTML/Markdown）
  excerpt       text,                           -- 摘要
  status        text default 'draft',           -- draft | publish | private
  comment_status text default 'open',
  published_at  timestamptz,
  created_at    timestamptz default now(),
  updated_at    timestamptz default now(),
  meta          jsonb default '{}'              -- 例如 { "cover_image": "..." }
);

-- 行级安全
alter table public.posts enable row level security;
create policy "作者可增删改查自己的文章"
  on public.posts for all
  using ( auth.uid() = author_id );

create policy "已发布文章对所有人可读"
  on public.posts for select
  using ( status = 'publish' );

-- 个人资料表（与 auth.users 一对一）
create table public.profiles (
  id           uuid primary key references auth.users on delete cascade,
  display_name text,
  avatar_url   text,
  role         text default 'author',       -- author | customer | admin 等
  created_at   timestamptz default now()
);

-- 行级安全
alter table public.profiles enable row level security;
create policy "只能查看/修改自己的资料"
  on public.profiles
  for all
  using ( auth.uid() = id );

-- 联系表单提交 (contact_submissions)
create table public.contact_submissions (
  id              bigint generated always as identity primary key,
  name            text not null,
  email           text not null,
  phone           text,                         -- 添加电话字段
  message         text not null,
  ip_address      text,
  user_agent      text,
  status          text default 'unread',       -- unread | read | replied
  request_type    text default 'contact',      -- contact | quote
  product_name    text,                        -- 询价产品名称
  product_sku     text,                        -- 询价产品SKU
  metadata        jsonb default '{}',          -- 额外的元数据
  created_at      timestamptz default now(),
  updated_at      timestamptz default now()
);

-- 为联系表单添加索引
create index idx_contact_submissions_status on public.contact_submissions(status);
create index idx_contact_submissions_request_type on public.contact_submissions(request_type);
create index idx_contact_submissions_email on public.contact_submissions(email);
create index idx_contact_submissions_created_at on public.contact_submissions(created_at desc);

-- 行级安全策略
alter table public.contact_submissions enable row level security;

-- 允许匿名用户插入联系表单
create policy "allow_anonymous_insert_contact"
  on public.contact_submissions for insert
  to anon
  with check (true);

-- 只有管理员可以查看所有联系表单
create policy "admin_can_view_all_contacts"
  on public.contact_submissions for select
  using (
    exists (
      select 1 from public.profiles 
      where id = auth.uid() 
      and role in ('admin', 'author')
    )
  );

-- 只有管理员可以更新联系表单状态
create policy "admin_can_update_contacts"
  on public.contact_submissions for update
  using (
    exists (
      select 1 from public.profiles 
      where id = auth.uid() 
      and role in ('admin', 'author')
    )
  );

-- 只有管理员可以删除联系表单
create policy "admin_can_delete_contacts"
  on public.contact_submissions for delete
  using (
    exists (
      select 1 from public.profiles 
      where id = auth.uid() 
      and role in ('admin', 'author')
    )
  );
