-- 创建存储桶（如果不存在）
INSERT INTO storage.buckets (id, name, public)
VALUES ('post-images', 'post-images', true)
ON CONFLICT (id) DO NOTHING;

-- 创建产品图片存储桶（如果不存在）
INSERT INTO storage.buckets (id, name, public)
VALUES ('product-images', 'product-images', true)
ON CONFLICT (id) DO NOTHING;

-- 设置post-images存储桶的RLS策略
-- 允许所有已认证用户上传图片
CREATE POLICY "允许已认证用户上传博客图片" ON storage.objects
  FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'post-images');

-- 允许用户访问自己上传的图片
CREATE POLICY "允许用户管理自己的博客图片" ON storage.objects
  FOR ALL
  TO authenticated
  USING (bucket_id = 'post-images' AND (storage.foldername(name))[1] = auth.uid()::text);

-- 允许公开访问图片
CREATE POLICY "允许公开访问博客图片" ON storage.objects
  FOR SELECT
  TO public
  USING (bucket_id = 'post-images');

-- 设置product-images存储桶的RLS策略
-- 允许所有已认证用户上传产品图片
CREATE POLICY "允许已认证用户上传产品图片" ON storage.objects
  FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'product-images');

-- 允许用户管理产品图片
CREATE POLICY "允许用户管理产品图片" ON storage.objects
  FOR ALL
  TO authenticated
  USING (bucket_id = 'product-images');

-- 允许公开访问产品图片
CREATE POLICY "允许公开访问产品图片" ON storage.objects
  FOR SELECT
  TO public
  USING (bucket_id = 'product-images');
