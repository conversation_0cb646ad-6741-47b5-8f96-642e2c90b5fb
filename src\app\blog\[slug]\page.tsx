import { createPublicClient } from '@/utils/supabase-public';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { Metadata } from 'next';
import Markdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Post, Profile } from '@/types/blog';

// Generate dynamic metadata
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const supabase = createPublicClient();
  
  const { data: post } = await supabase
    .from('posts')
    .select('title,excerpt')
    .eq('slug', params.slug)
    .single();
  
  if (!post) {
    return {
      title: 'Guide Not Found - PRSPARES',
      description: 'Sorry, the repair guide or article you requested does not exist.'
    };
  }
  
  return {
    title: `${post.title} - PRSPARES Repair Guides`,
    description: post.excerpt || `Read our expert guide on ${post.title}`
  };
}

export default async function BlogPostPage({ params }: { params: { slug: string } }) {
  const supabase = createPublicClient();
  
  try {
    const { data: post, error } = await supabase
      .from('posts')
      .select(`
        id,
        title,
        slug,
        content,
        excerpt,
        status,
        published_at,
        meta,
        author_id,
        profiles:profiles(id, display_name, avatar_url)
      `)
      .eq('slug', params.slug)
      .eq('status', 'publish')
      .single();
    
    if (error) {
      console.error('Error fetching post:', error);
      throw error;
    }
    
    if (!post) {
      notFound();
    }

    const typedPost = post as unknown as Post;
    const authorProfile = typedPost.profiles as unknown as Profile;

    const wordCount = typedPost.content ? typedPost.content.split(/\s+/).length : 0;
    const readTimeMin = Math.max(1, Math.ceil(wordCount / 200));
    const publishDate = typedPost.published_at ? new Date(typedPost.published_at).toLocaleDateString('en-US') : '';
    const coverImage = typedPost.meta?.cover_image || 'https://picsum.photos/seed/prspares-detail/1200/800';
    const authorName = authorProfile?.display_name || 'PRSPARES Team';
    const authorAvatar = authorProfile?.avatar_url;

    const { data: relatedPosts } = await supabase
      .from('posts')
      .select('id, title, slug, excerpt, published_at, meta')
      .eq('status', 'publish')
      .neq('id', post.id)
      .limit(3)
      .order('published_at', { ascending: false });
    
    return (
      <main className="min-h-screen bg-gray-100">
        <div className="relative h-[55vh] min-h-[450px] overflow-hidden shadow-lg">
          <div className="absolute inset-0">
            <Image 
              src={coverImage} 
              alt={typedPost.title} 
              fill
              style={{objectFit: 'cover'}}
              priority
              className="transform scale-105 group-hover:scale-110 transition-transform duration-500 ease-in-out"
            />
          </div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/50 to-transparent"></div>
          
          <div className="relative z-10 h-full flex flex-col justify-end pb-12 md:pb-16">
            <div className="max-w-[850px] mx-auto px-4 w-full">
              <nav className="mb-4 md:mb-6">
                <div className="flex items-center text-sm text-white/80 space-x-2">
                  <Link href="/" className="hover:text-white transition-colors duration-200">Home</Link>
                  <span>/</span>
                  <Link href="/blog" className="hover:text-white transition-colors duration-200">Guides</Link>
                  <span>/</span>
                  <span className="text-white font-medium truncate max-w-xs md:max-w-sm">{typedPost.title}</span>
                </div>
              </nav>
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-white mb-5 leading-tight shadow-text">
                {typedPost.title}
              </h1>
              <div className="flex items-center text-white/90">
                {authorAvatar && (
                  <Image 
                    src={authorAvatar} 
                    alt={authorName} 
                    width={52} 
                    height={52} 
                    className="rounded-full border-2 border-white/30 shadow-md mr-4"
                  />
                )}
                {!authorAvatar && (
                    <div className="w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center text-white text-xl font-semibold mr-4 border-2 border-white/30 shadow-md">
                        {authorName.charAt(0)}
                    </div>
                )}
                <div>
                  <p className="font-semibold text-lg text-white">{authorName}</p>
                  <div className="flex items-center text-sm text-white/80 space-x-2">
                    <span>{publishDate}</span>
                    <span className="opacity-50">•</span>
                    <span>{readTimeMin} min read</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="py-12 md:py-16 bg-gray-100">
          <div className="max-w-[1200px] mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-10 lg:gap-12">
              <div className="lg:w-[calc(66.666%-1.25rem)] xl:w-[calc(66.666%-1.5rem)]">
                <div className="bg-white rounded-xl shadow-lg p-6 md:p-8 lg:p-10">
                  <article className="prose prose-lg max-w-none prose-headings:font-semibold prose-headings:text-gray-800 prose-a:text-[#00B140] hover:prose-a:text-[#008631] prose-strong:font-semibold prose-img:rounded-lg prose-img:shadow-md">
                    <Markdown 
                      remarkPlugins={[remarkGfm]}
                      rehypePlugins={[rehypeRaw]}
                    >
                      {typedPost.content || 'Content not available.'}
                    </Markdown>
                  </article>
                  
                  {typedPost.meta?.tags && Array.isArray(typedPost.meta.tags) && typedPost.meta.tags.length > 0 && (
                    <div className="mt-10 border-t border-gray-200 pt-8">
                      <h3 className="text-xl font-bold mb-4 text-gray-800">Related Tags</h3>
                      <div className="flex flex-wrap gap-3">
                        {typedPost.meta.tags.map((tag: string) => (
                          <Link key={tag} href={`/blog?tag=${encodeURIComponent(tag.toLowerCase())}`} className="px-4 py-1.5 bg-green-50 text-green-700 rounded-full text-sm font-medium hover:bg-green-100 hover:text-green-800 transition-colors duration-200 shadow-sm">
                            #{tag}
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              <aside className="lg:w-[calc(33.333%-1.25rem)] xl:w-[calc(33.333%-1.5rem)]">
                <div className="bg-white p-6 rounded-xl shadow-lg sticky top-24">
                  <h3 className="text-2xl font-bold mb-6 text-gray-900">Related Guides</h3>
                  {relatedPosts && relatedPosts.length > 0 ? (
                    <div className="space-y-5">
                      {(relatedPosts as any[]).map((relatedPost: any) => (
                        <Link key={relatedPost.id} href={`/blog/${relatedPost.slug}`} className="group block p-4 rounded-lg hover:bg-gray-50 transition-all duration-200 border border-transparent hover:border-gray-200">
                          <h4 className="font-semibold text-lg text-gray-800 group-hover:text-[#00B140] transition-colors duration-200 mb-1 line-clamp-2">
                            {relatedPost.title}
                          </h4>
                          <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                            {relatedPost.excerpt}
                          </p>
                          <span className="text-xs text-[#00B140] font-medium group-hover:underline">Read guide →</span>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-600 italic">No related guides available at the moment.</p>
                  )}
                </div>
              </aside>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-green-500 via-[#00B140] to-teal-600 py-16 md:py-20">
          <div className="max-w-[1200px] mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-5">Need Specific Parts or Advice?</h2>
            <p className="text-white/90 mb-8 max-w-xl mx-auto text-lg">
              Can't find what you're looking for or need expert help? Contact PRSPARES today!
            </p>
            <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
              <Link href="/products" className="bg-white hover:bg-gray-100 text-[#00B140] font-semibold py-3.5 px-8 rounded-lg transition-colors duration-200 shadow-md text-lg">
                Browse All Parts
              </Link>
              <Link href="/contact" className="border-2 border-white hover:bg-white/10 text-white font-semibold py-3.5 px-8 rounded-lg transition-colors duration-200 text-lg">
                Contact Our Experts
              </Link>
            </div>
          </div>
        </div>
      </main>
    );
  } catch (error) {
    console.error('Guide page rendering error:', error);
    notFound();
  }
}
