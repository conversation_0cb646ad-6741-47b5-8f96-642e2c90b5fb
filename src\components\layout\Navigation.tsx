'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface NavigationProps {
  orientation?: 'horizontal' | 'vertical';
}

const Navigation = ({ orientation = 'horizontal' }: NavigationProps) => {
  const pathname = usePathname();
  
  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'About', path: '/about' },
    { name: 'Blog', path: '/blog' },
    { name: 'Quiz', path: '/quiz' },
    { name: 'Products', path: '/products' },
    { name: 'Contact', path: '/contact' },
  ];

  return (
    <nav className={`flex ${orientation === 'vertical' ? 'flex-col space-y-3' : 'space-x-8'}`}>
      {navItems.map((item) => {
        const isActive = pathname === item.path;
        return (
          <Link
            key={item.path}
            href={item.path}
            className={`
              ${orientation === 'vertical' 
                ? `w-full py-3 px-4 rounded-lg text-left transition-all duration-200 ${
                    isActive 
                      ? 'text-[#00B140] bg-green-50 font-semibold' 
                      : 'text-gray-700 hover:text-[#00B140] hover:bg-gray-50'
                  }`
                : `relative py-2 px-3 rounded-lg font-medium transition-all duration-200 ${
                    isActive 
                      ? 'text-[#00B140] bg-green-50' 
                      : 'text-gray-700 hover:text-[#00B140] hover:bg-gray-50'
                  }`
              }
            `}
          >
            {item.name}
            {isActive && orientation === 'horizontal' && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-[#00B140] rounded-full"></div>
            )}
          </Link>
        );
      })}
    </nav>
  );
};

export default Navigation;
